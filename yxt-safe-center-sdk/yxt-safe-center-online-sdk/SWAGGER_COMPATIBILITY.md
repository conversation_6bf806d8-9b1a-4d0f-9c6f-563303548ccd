# Swagger 兼容性说明

## 概述

ControllerScanner 现在支持 Swagger2 和 Swagger3/OpenAPI3 的兼容性，可以自动检测并适配不同版本的 Swagger 注解。

## 支持的版本

- **Swagger2**: 使用 `io.swagger.annotations.*` 注解
- **Swagger3/OpenAPI3**: 使用 `io.swagger.v3.oas.annotations.*` 注解

## 版本检测机制

系统会按以下优先级检测 Swagger 版本：

1. **配置优先**: 检查 `swagger.version` 配置属性
2. **自动检测**: 检测类路径中的 Swagger 依赖
   - 优先检测 Swagger3/OpenAPI3
   - 其次检测 Swagger2
   - 都没有则使用默认适配器

## 配置方式

### 方式1：在 application.yml 中配置

```yaml
swagger:
  version: swagger2  # 可选值: swagger2, swagger3, openapi3, 2, 3
```

### 方式2：通过环境变量

```bash
export SWAGGER_VERSION=swagger3
```

### 方式3：通过 JVM 参数

```bash
-Dswagger.version=swagger2
```

## 注解映射关系

### 接口描述注解

| Swagger2 | Swagger3 | 用途 |
|----------|----------|------|
| `@ApiOperation(value="描述")` | `@Operation(summary="描述")` | 接口名称 |
| `@ApiOperation(notes="详细描述")` | `@Operation(description="详细描述")` | 接口详细描述 |

### 参数描述注解

| Swagger2 | Swagger3 | 用途 |
|----------|----------|------|
| `@ApiParam(value="参数描述")` | `@Parameter(description="参数描述")` | 参数描述 |

### 模型描述注解

| Swagger2 | Swagger3 | 用途 |
|----------|----------|------|
| `@ApiModel(value="模型名称")` | `@Schema(name="模型名称")` | 模型名称 |
| `@ApiModel(description="模型描述")` | `@Schema(description="模型描述")` | 模型描述 |
| `@ApiModelProperty(value="字段描述")` | `@Schema(description="字段描述")` | 字段描述 |

## 使用示例

### Swagger2 示例

```java
@RestController
@RequestMapping("/api/v1/users")
public class UserController {
    
    @ApiOperation(value = "获取用户信息", notes = "根据用户ID获取用户详细信息")
    @GetMapping("/{id}")
    public ResponseBase<UserInfo> getUser(
        @ApiParam(value = "用户ID", required = true) @PathVariable Long id) {
        // 实现逻辑
    }
}

@ApiModel(value = "用户信息", description = "用户基本信息模型")
public class UserInfo {
    @ApiModelProperty(value = "用户ID")
    private Long id;
    
    @ApiModelProperty(value = "用户名")
    private String username;
}
```

### Swagger3 示例

```java
@RestController
@RequestMapping("/api/v1/users")
public class UserController {
    
    @Operation(summary = "获取用户信息", description = "根据用户ID获取用户详细信息")
    @GetMapping("/{id}")
    public ResponseBase<UserInfo> getUser(
        @Parameter(description = "用户ID", required = true) @PathVariable Long id) {
        // 实现逻辑
    }
}

@Schema(name = "用户信息", description = "用户基本信息模型")
public class UserInfo {
    @Schema(description = "用户ID")
    private Long id;
    
    @Schema(description = "用户名")
    private String username;
}
```

## 依赖配置

### Maven 依赖

```xml
<!-- Swagger2 依赖 (可选) -->
<dependency>
    <groupId>io.springfox</groupId>
    <artifactId>springfox-swagger2</artifactId>
    <scope>provided</scope>
    <optional>true</optional>
</dependency>

<!-- Swagger3/OpenAPI3 依赖 (可选) -->
<dependency>
    <groupId>org.springdoc</groupId>
    <artifactId>springdoc-openapi-ui</artifactId>
    <version>1.7.0</version>
    <scope>provided</scope>
    <optional>true</optional>
</dependency>
```

## 注意事项

1. **向后兼容**: 现有的 Swagger2 注解仍然可以正常工作
2. **自动适配**: 无需修改现有代码，系统会自动适配
3. **配置建议**: 建议明确配置 `swagger.version` 以避免自动检测的不确定性
4. **依赖管理**: 依赖标记为 `optional=true`，不会强制引入特定版本
5. **性能影响**: 版本检测只在启动时执行一次，不影响运行时性能

## 故障排除

### 问题1: 注解没有被解析

**原因**: 可能是版本检测错误或依赖缺失

**解决方案**:
1. 检查类路径中是否包含对应的 Swagger 依赖
2. 明确配置 `swagger.version`
3. 查看启动日志中的版本检测信息

### 问题2: 配置不生效

**原因**: 配置属性名称错误或格式不正确

**解决方案**:
1. 确认配置属性名为 `swagger.version`
2. 确认配置值为支持的格式: `swagger2`, `swagger3`, `openapi3`, `2`, `3`

### 问题3: 同时存在两个版本的依赖

**原因**: 项目中同时引入了 Swagger2 和 Swagger3 依赖

**解决方案**:
1. 明确配置 `swagger.version` 指定使用的版本
2. 排除不需要的依赖
3. 检查传递依赖是否引入了多个版本
