package com.yxt.safecenter.sdk.online.core;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

/**
 * ControllerScanner 测试类
 * 测试 Swagger2 和 Swagger3 兼容性
 */
@SpringBootTest
@TestPropertySource(properties = {
    "biz.safecenter.online.open=false", // 禁用实际的接口上报
    "spring.profiles.active=test"
})
public class ControllerScannerTest {

    @Test
    public void testSwagger2Detection() {
        // 测试 Swagger2 版本检测
        // 这个测试需要在有 Swagger2 依赖的环境中运行
        System.out.println("测试 Swagger2 版本检测");
    }

    @Test
    public void testSwagger3Detection() {
        // 测试 Swagger3 版本检测
        // 这个测试需要在有 Swagger3 依赖的环境中运行
        System.out.println("测试 Swagger3 版本检测");
    }

    @Test
    public void testConfiguredVersion() {
        // 测试通过配置指定版本
        System.out.println("测试配置指定版本");
    }
}
