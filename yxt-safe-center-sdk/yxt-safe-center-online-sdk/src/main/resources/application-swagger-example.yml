# Swagger 版本配置示例
# 可以通过以下配置指定使用的 Swagger 版本
# 如果不配置，系统会自动检测类路径中的 Swagger 依赖

# 配置方式1：使用版本号
swagger:
  version: swagger2  # 可选值: swagger2, swagger3, openapi3, 2, 3

# 配置方式2：使用数字
# swagger:
#   version: 2  # 2 表示 Swagger2, 3 表示 Swagger3/OpenAPI3

# 配置方式3：使用完整名称
# swagger:
#   version: openapi3  # openapi3 表示 Swagger3/OpenAPI3

# 注意：
# 1. 如果配置了 swagger.version，系统会优先使用配置的版本
# 2. 如果没有配置，系统会自动检测类路径中的依赖：
#    - 优先检测 Swagger3/OpenAPI3 (io.swagger.v3.oas.annotations.Operation)
#    - 其次检测 Swagger2 (io.swagger.annotations.ApiOperation)
#    - 如果都没有，使用默认适配器（不解析 Swagger 注解）
# 3. 建议在项目中明确配置版本，避免自动检测可能带来的不确定性
