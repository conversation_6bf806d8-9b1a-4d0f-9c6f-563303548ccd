package com.yxt.safecenter.sdk.online.core;

import jakarta.servlet.http.HttpServletRequestWrapper;

import java.net.URI;

/**
 * Since: 2025/08/01 14:58
 * Author: qs
 */
public class FakeHttpServletRequest extends HttpServletRequestWrapper {

    private final String scheme;
    private final String serverName;
    private final int serverPort;
    private final String contextPath;
    private final String servletPath;
    private final String requestUri;

    public FakeHttpServletRequest(String url) {
        super(new HttpServletRequestAdapter());
        URI uri = URI.create(url);
        this.scheme = uri.getScheme();
        this.serverName = uri.getHost();
        this.serverPort = uri.getPort() != -1 ? uri.getPort() : ("https".equals(scheme) ? 443 : 80);
        this.contextPath = "";
        this.servletPath = "";
        this.requestUri = uri.getPath();
    }

    @Override
    public StringBuffer getRequestURL() {
        return new StringBuffer(scheme + "://" + serverName + (serverPort != 80 && serverPort != 443 ? ":" + serverPort : "") + requestUri);
    }

    @Override
    public String getRequestURI() {
        return requestUri;
    }

    @Override
    public String getServerName() {
        return serverName;
    }

    @Override
    public int getServerPort() {
        return serverPort;
    }

    @Override
    public String getContextPath() {
        return contextPath;
    }

    @Override
    public String getServletPath() {
        return servletPath;
    }
}

