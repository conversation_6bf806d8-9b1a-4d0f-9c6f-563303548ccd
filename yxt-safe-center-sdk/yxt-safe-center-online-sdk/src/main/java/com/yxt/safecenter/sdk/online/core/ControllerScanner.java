package com.yxt.safecenter.sdk.online.core;

import com.google.common.collect.Sets;
import com.yxt.lang.util.JsonUtils;
import com.yxt.safecenter.sdk.online.api.SafeInterfaceApi;
import com.yxt.safecenter.sdk.online.core.analyzer.ParameterAnalyzer;
import com.yxt.safecenter.sdk.online.core.analyzer.ResponseAnalyzer;
import com.yxt.safecenter.sdk.online.core.extractor.ApiInfoExtractor;
import com.yxt.safecenter.sdk.online.core.resolver.GenericTypeResolver;
import com.yxt.safecenter.sdk.online.core.schema.SchemaGenerator;
import com.yxt.safecenter.sdk.online.core.swagger.SwaggerAdapterFactory;
import com.yxt.safecenter.sdk.online.core.swagger.SwaggerAnnotationAdapter;
import com.yxt.safecenter.sdk.online.dto.request.SafeInterfaceOnlineReq;
import com.yxt.safecenter.sdk.online.enums.ReqMethod;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.ApplicationContext;

import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.mvc.method.RequestMappingInfo;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;

import java.lang.annotation.Annotation;
import java.lang.reflect.*;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
@RequiredArgsConstructor
public class ControllerScanner implements CommandLineRunner {

    private final RequestMappingHandlerMapping requestMappingHandlerMapping;
    private final SafeInterfaceApi safeInterfaceApi;
    private final Environment environment;
    private final ApplicationContext applicationContext;
    private Set<String> projectPackagePres = Sets.newHashSet("cn.hydee", "com.hydee");

    // 组件依赖
    private SwaggerAnnotationAdapter swaggerAdapter;
    private ApiInfoExtractor apiInfoExtractor;
    private ParameterAnalyzer parameterAnalyzer;
    private ResponseAnalyzer responseAnalyzer;

    @Override
    public void run(String... args) throws Exception {
        String[] activeProfiles = environment.getActiveProfiles();
        String applicationName = environment.getProperty("spring.application.name");
        String property = environment.getProperty("biz.safecenter.online.open");
        String activeProfile = activeProfiles.length == 0 ? "default" : StringUtils.join(activeProfiles, ",");
        log.info("-------------applicationName: " + applicationName);
        log.info("-------------activeProfile: " + activeProfile);
        log.info("--------------------------");
        if ("local".equals(activeProfile) || (property != null && property.equals(Boolean.FALSE.toString()))) {
            return;
        }

        // 初始化组件
        initComponents();

        // 获取所有的请求映射信息
        Map<RequestMappingInfo, HandlerMethod> handlerMethods = requestMappingHandlerMapping.getHandlerMethods();

        List<SafeInterfaceOnlineReq> serverReqList = new ArrayList<>();
        Set<String> needSkipControllerSet = new HashSet<>();
        // 遍历所有的请求映射
        for (Map.Entry<RequestMappingInfo, HandlerMethod> entry : handlerMethods.entrySet()) {
            RequestMappingInfo mappingInfo = entry.getKey();
            HandlerMethod handlerMethod = entry.getValue();

            // 获取控制器类的包名
            String packageName = handlerMethod.getBeanType().getPackage().getName();

            // 过滤接口
            if (invalidPackage(handlerMethod, packageName)) {
                needSkipControllerSet.add(handlerMethod.getBeanType().getSimpleName());
                continue;
            }

            // 获取请求路径 - 兼容Spring Boot 3.x
            List<String> apiPath = ApiInfoExtractor.resolveApiPaths(mappingInfo);

//            List<String> apiPath = new ArrayList<>();
//            if (mappingInfo.getPatternsCondition() != null) {
//                apiPath.addAll(mappingInfo.getPatternsCondition().getPatterns());
//            } else if (mappingInfo.getPathPatternsCondition() != null) {
//                mappingInfo.getPathPatternsCondition().getDirectPaths().forEach(apiPath::add);
//            }

            // 获取HTTP方法类型
            List<String> reqMethods = new ArrayList<>();
            if (CollectionUtils.isEmpty(mappingInfo.getMethodsCondition().getMethods())) {
                reqMethods = Arrays.stream(ReqMethod.values()).map(ReqMethod::name).collect(Collectors.toList());
            } else {
                for (RequestMethod httpMethod : mappingInfo.getMethodsCondition().getMethods()) {
                    reqMethods.add(httpMethod.name());
                }
            }

            // 获取控制器方法的类名和方法名
            String className = handlerMethod.getBeanType().getSimpleName();
            String methodName = handlerMethod.getMethod().getName();
            Method method = handlerMethod.getMethod();

            // 解析请求参数信息
            String reqInfo = parameterAnalyzer.parseRequestInfo(method);
            // 解析响应参数信息
            String respInfo = responseAnalyzer.parseResponseInfo(method);
            // 解析接口名称信息
            String apiName = apiInfoExtractor.parseApiName(method);

            SafeInterfaceOnlineReq safeInterfaceOnlineReq = new SafeInterfaceOnlineReq();
            safeInterfaceOnlineReq.setApplicationName(applicationName);
            safeInterfaceOnlineReq.setApiClass(className);
            safeInterfaceOnlineReq.setApiMethod(methodName);
//            safeInterfaceOnlineReq.setNeibu(isFeignClient(handlerMethod)); // 是否是内部接口 todo:gx
            safeInterfaceOnlineReq.setApiWay(JsonUtils.toJson(reqMethods));
            safeInterfaceOnlineReq.setApiPath(JsonUtils.toJson(apiPath));
            safeInterfaceOnlineReq.setReqParamsInfo(reqInfo);
            safeInterfaceOnlineReq.setRespParamsInfo(respInfo);
            safeInterfaceOnlineReq.setApiName(apiName);
            serverReqList.add(safeInterfaceOnlineReq);
        }
        log.info("需要过滤的非本项目接口类：{}", JsonUtils.toJson(needSkipControllerSet));

        // 如果检测到条件不符合要求，则提前终止
        try {
            log.info("------------------------------------数据list,{}", JsonUtils.toJson(serverReqList));
            safeInterfaceApi.online(serverReqList);
        } catch (Exception e) {
            log.error("------------------------------------接口上报异常:终止应用！", e);
            System.exit(1);
        }
    }

    /**
     * 初始化所有组件
     */
    private void initComponents() {
        // 初始化项目包名前缀
        initProjectPackagePres();

        // 初始化 Swagger 适配器
        swaggerAdapter = SwaggerAdapterFactory.createAdapter(environment);

        // 初始化各个组件
        apiInfoExtractor = new ApiInfoExtractor(swaggerAdapter);

        SchemaGenerator schemaGenerator = new SchemaGenerator(swaggerAdapter, null); // GenericTypeResolver会在后面设置
        GenericTypeResolver genericTypeResolver = new GenericTypeResolver(schemaGenerator);

        // 更新SchemaGenerator的GenericTypeResolver引用
        schemaGenerator = new SchemaGenerator(swaggerAdapter, genericTypeResolver);

        parameterAnalyzer = new ParameterAnalyzer(swaggerAdapter, schemaGenerator);
        responseAnalyzer = new ResponseAnalyzer(schemaGenerator, genericTypeResolver);
    }

    private static List<String> resolveApiPaths(RequestMappingInfo mappingInfo) {
        List<String> paths = new ArrayList<>();
        try {
            Method getPathPatternsConditionMethod = mappingInfo.getClass().getMethod("getPathPatternsCondition");
            Object pathPatternsCondition = getPathPatternsConditionMethod.invoke(mappingInfo);
            if (pathPatternsCondition != null) {
                Method getDirectPaths = pathPatternsCondition.getClass().getMethod("getDirectPaths");
                Collection<?> directPaths = (Collection<?>) getDirectPaths.invoke(pathPatternsCondition);
                for (Object path : directPaths) {
                    paths.add(path.toString());
                }
            } else {
                // Spring Boot 2.x fallback
                Set<String> oldPaths = mappingInfo.getPatternsCondition().getPatterns();
                if (oldPaths != null) {
                    paths.addAll(oldPaths);
                }
            }
        } catch (NoSuchMethodException e) {
            // Spring Boot 2.x
            Set<String> oldPaths = mappingInfo.getPatternsCondition().getPatterns();
            if (oldPaths != null) {
                paths.addAll(oldPaths);
            }
        } catch (Exception e) {
            throw new RuntimeException("解析 RequestMappingInfo 失败", e);
        }
        return paths;
    }

    private boolean invalidPackage(HandlerMethod handlerMethod, String packageName) {
        boolean skip = true;
        for (String packagePre : projectPackagePres) {
            if (packageName.startsWith(packagePre)) {
                skip = false;
                break; // 如果不在指定的包内，跳过当前控制器
            }
        }
        return skip;
    }

    public boolean isFeignClient(HandlerMethod handlerMethod) {
        // 获取控制器类的 Class 对象
        Class<?> controllerClass = handlerMethod.getBeanType();
        Annotation[] annotations = controllerClass.getAnnotations();
        for (Annotation annotation : annotations) {
            if (annotation instanceof FeignClient) {
                return true;
            }
        }
        // 获取控制器类实现的所有接口
        Class<?>[] interfaces = controllerClass.getInterfaces();

        for (Class<?> iface : interfaces) {
            if (iface.getAnnotation(FeignClient.class) != null) {
                return true;
            }
        }
        return false;
    }











    /**
     * 初始化当前项目的包名前缀（基于主应用类的包）
     */
    private void initProjectPackagePres() {
        try {
            // 查找带有@SpringBootApplication注解的主类（项目入口类）
            Map<String, Object> bootBeans = applicationContext.getBeansWithAnnotation(SpringBootApplication.class);
            if (!bootBeans.isEmpty()) {
                // 取第一个主类（通常一个项目只有一个）
                Class<?> mainClass = bootBeans.values().iterator().next().getClass();
                // 获取主类的包名（即项目根包）
                String mainPackage = mainClass.getPackage().getName();
                projectPackagePres.add(mainPackage);
            } else {
                // 未找到主类时，fallback到原有默认前缀
                log.warn("未找到主应用类，使用默认包名前缀");
            }
        } catch (Exception e) {
            // 异常时fallback到默认前缀
            log.error("获取项目包名前缀失败，使用默认值", e);
        }
    }

    private boolean invalidPackage(HandlerMethod handlerMethod, String packageName) {
        boolean skip = true;
        for (String packagePre : projectPackagePres) {
            if (packageName.startsWith(packagePre)) {
                skip = false;
                break; // 如果不在指定的包内，跳过当前控制器
            }
        }
        return skip;
    }

    public boolean isFeignClient(HandlerMethod handlerMethod) {
        // 获取控制器类的 Class 对象
        Class<?> controllerClass = handlerMethod.getBeanType();
        Annotation[] annotations = controllerClass.getAnnotations();
        for (Annotation annotation : annotations) {
            if (annotation instanceof FeignClient) {
                return true;
            }
        }
        // 获取控制器类实现的所有接口
        Class<?>[] interfaces = controllerClass.getInterfaces();

        for (Class<?> iface : interfaces) {
            if (iface.getAnnotation(FeignClient.class) != null) {
                return true;
            }
        }
        return false;
    }

}
                            }
                        }
                    } else if (itemType instanceof ParameterizedType) {
                        // 处理嵌套的泛型类型，如List<ResponseBase<DemoResp>>中的ResponseBase<DemoResp>
                        ParameterizedType itemParamType = (ParameterizedType) itemType;
                        Class<?> itemRawType = (Class<?>) itemParamType.getRawType();
                        String itemSchemaType = getJsonSchemaType(itemRawType);
                        items.put("type", itemSchemaType);

                        if ("object".equals(itemSchemaType)) {
                            // 递归调用createTypeSchema来处理复杂的泛型类型
                            Map<String, Object> itemSchema = createTypeSchema(itemRawType, itemType);
                            // 将itemSchema的内容合并到items中，但排除type字段（已经设置过了）
                            for (Map.Entry<String, Object> entry : itemSchema.entrySet()) {
                                if (!"type".equals(entry.getKey())) {
                                    items.put(entry.getKey(), entry.getValue());
                                }
                            }
                        }
                    }
                }
            }
            // 处理数组类型，如String[]、SomeClass[]等
            else if (arrayClass.isArray()) {
                Class<?> componentType = arrayClass.getComponentType();
                String itemSchemaType = getJsonSchemaType(componentType);
                items.put("type", itemSchemaType);

                // 如果是枚举类型，添加enum属性
                if ("string".equals(itemSchemaType) && componentType.isEnum()) {
                    List<String> enumValues = getEnumValues(componentType);
                    if (!enumValues.isEmpty()) {
                        items.put("enums", enumValues);
                    }
                } else if ("object".equals(itemSchemaType)) {
                    // 检查是否已经访问过此类，防止循环引用
                    if (!visitedClasses.contains(componentType)) {
                        // 添加对象描述
                        String description = getClassDescription(componentType);
                        if (StringUtils.isNotBlank(description)) {
                            items.put("description", description);
                        }

                        Map<String, Object> properties = createPropertiesFromClass(componentType, visitedClasses);
                        if (!properties.isEmpty()) {
                            items.put("properties", properties);
                        }
                    } else {
                        // 循环引用时，只保留基本信息
                        items.put("description", "循环引用: " + componentType.getSimpleName());
                    }
                }
            }
        } catch (Exception e) {
            log.error("创建数组items schema失败", e);
        }

        return items;
    }

    /**
     * 解析类的字段信息（包括父类字段）
     */
    private Map<String, Object> parseClassFieldsWithInheritance(Class<?> clazz) {
        Map<String, Object> fieldsInfo = new HashMap<>();

        if (isBasicType(clazz)) {
            return fieldsInfo;
        }

        try {
            // 获取当前类及所有父类的字段
            Class<?> currentClass = clazz;
            while (currentClass != null && currentClass != Object.class) {
                Field[] fields = currentClass.getDeclaredFields();
                for (Field field : fields) {
                    // 跳过静态字段和serialVersionUID，以及已经处理过的字段
                    if (java.lang.reflect.Modifier.isStatic(field.getModifiers()) ||
                            "serialVersionUID".equals(field.getName()) ||
                            fieldsInfo.containsKey(field.getName())) {
                        continue;
                    }

                    Map<String, Object> fieldInfo = new HashMap<>();
                    fieldInfo.put("name", field.getName());
                    fieldInfo.put("type", getSimpleTypeName(field.getType()));
                    fieldInfo.put("required", isFieldRequired(field));
                    fieldInfo.put("description", getFieldDescription(field));
                    fieldsInfo.put(field.getName(), fieldInfo);
                }

                // 移动到父类
                currentClass = currentClass.getSuperclass();
            }
        } catch (Exception e) {
            log.error("解析类字段失败: {}", clazz.getName(), e);
        }

        return fieldsInfo;
    }

    /**
     * 判断参数是否必须
     */
    private boolean isParameterRequired(Parameter parameter) {
        // 检查@NotNull、@NotBlank等验证注解
        if (parameter.isAnnotationPresent(NotNull.class) ||
                parameter.isAnnotationPresent(NotBlank.class) ||
                parameter.isAnnotationPresent(NotEmpty.class)) {
            return true;
        }

        // 检查Spring注解的required属性
        if (parameter.isAnnotationPresent(RequestParam.class)) {
            return parameter.getAnnotation(RequestParam.class).required();
        }
        if (parameter.isAnnotationPresent(RequestHeader.class)) {
            return parameter.getAnnotation(RequestHeader.class).required();
        }
        if (parameter.isAnnotationPresent(PathVariable.class)) {
            return parameter.getAnnotation(PathVariable.class).required();
        }

        return false;
    }

    /**
     * 判断字段是否必须
     */
    private boolean isFieldRequired(Field field) {
        return field.isAnnotationPresent(NotNull.class) ||
                field.isAnnotationPresent(NotBlank.class) ||
                field.isAnnotationPresent(NotEmpty.class);
    }

    /**
     * 获取参数描述
     */
    private String getParameterDescription(Parameter parameter) {
        if (swaggerAdapter != null) {
            String description = swaggerAdapter.getApiParamValue(parameter);
            if (StringUtils.isNotBlank(description)) {
                return description;
            }
        }

        // 兼容原有的直接注解方式（向后兼容）
        if (parameter.isAnnotationPresent(ApiParam.class)) {
            return parameter.getAnnotation(ApiParam.class).value();
        }
        return "";
    }

    /**
     * 获取字段描述
     */
    private String getFieldDescription(Field field) {
        if (swaggerAdapter != null) {
            String description = swaggerAdapter.getApiModelPropertyValue(field);
            if (StringUtils.isNotBlank(description)) {
                return description;
            }
        }

        // 兼容原有的直接注解方式（向后兼容）
        if (field.isAnnotationPresent(ApiModelProperty.class)) {
            return field.getAnnotation(ApiModelProperty.class).value();
        }
        return "";
    }

    /**
     * 获取类描述（从ApiModel注解）
     */
    private String getClassDescription(Class<?> clazz) {
        if (swaggerAdapter != null) {
            String value = swaggerAdapter.getApiModelValue(clazz);
            if (StringUtils.isNotBlank(value)) {
                return value;
            }
            String description = swaggerAdapter.getApiModelDescription(clazz);
            if (StringUtils.isNotBlank(description)) {
                return description;
            }
        }

        // 兼容原有的直接注解方式（向后兼容）
        if (clazz.isAnnotationPresent(ApiModel.class)) {
            ApiModel apiModel = clazz.getAnnotation(ApiModel.class);
            String value = apiModel.value();
            if (StringUtils.isNotBlank(value)) {
                return value;
            }
            String description = apiModel.description();
            if (StringUtils.isNotBlank(description)) {
                return description;
            }
        }
        return "";
    }

    /**
     * 解析接口名称信息
     */
    private String parseApiName(Method method) {
        if (swaggerAdapter != null) {
            String value = swaggerAdapter.getApiOperationValue(method);
            if (StringUtils.isNotEmpty(value)) {
                return value;
            }
        }

        // 兼容原有的直接注解方式（向后兼容）
        if (method.isAnnotationPresent(ApiOperation.class)) {
            ApiOperation apiOperation = method.getAnnotation(ApiOperation.class);
            String value = apiOperation.value();
            if (StringUtils.isNotEmpty(value)) {
                return value;
            }
        }

        return "";
    }

    /**
     * 获取枚举的所有值
     */
    private List<String> getEnumValues(Class<?> enumClass) {
        List<String> enumValues = new ArrayList<>();
        if (enumClass.isEnum()) {
            try {
                Object[] enumConstants = enumClass.getEnumConstants();
                for (Object enumConstant : enumConstants) {
                    enumValues.add(((Enum<?>) enumConstant).name());
                }
            } catch (Exception e) {
                log.error("获取枚举值失败: {}", enumClass.getName(), e);
            }
        }
        return enumValues;
    }

    /**
     * 判断是否为基本类型
     */
    private boolean isBasicType(Class<?> clazz) {
        return clazz.isPrimitive() ||
                clazz == String.class ||
                clazz == Integer.class || clazz == int.class ||
                clazz == Long.class || clazz == long.class ||
                clazz == Double.class || clazz == double.class ||
                clazz == Float.class || clazz == float.class ||
                clazz == Boolean.class || clazz == boolean.class ||
                clazz == Byte.class || clazz == byte.class ||
                clazz == Short.class || clazz == short.class ||
                clazz == Character.class || clazz == char.class ||
                clazz == Void.class || clazz == void.class ||
                Number.class.isAssignableFrom(clazz);
    }

    /**
     * 获取简单类型名称
     */
    private String getSimpleTypeName(Class<?> clazz) {
        if (clazz == int.class || clazz == Integer.class) return "integer";
        if (clazz == long.class || clazz == Long.class) return "long";
        if (clazz == double.class || clazz == Double.class) return "double";
        if (clazz == float.class || clazz == Float.class) return "float";
        if (clazz == boolean.class || clazz == Boolean.class) return "boolean";
        if (clazz == String.class) return "string";
        return clazz.getSimpleName();
    }

    /**
     * 创建基本类型信息
     */
    private Map<String, Object> createBasicTypeInfo(Class<?> clazz, String description) {
        Map<String, Object> info = new HashMap<>();
        info.put("type", getSimpleTypeName(clazz));
        info.put("description", description);
        info.put("required", false);

        return info;
    }

    /**
     * 解析泛型类型
     */
    private Map<String, Object> parseGenericType(Type type) {
        Map<String, Object> typeInfo = new HashMap<>();

        try {
            if (type instanceof Class) {
                Class<?> clazz = (Class<?>) type;
                if (!isBasicType(clazz) && !clazz.equals(Void.class)) {
                    typeInfo = parseClassFieldsWithInheritance(clazz);
                } else if (!clazz.equals(Void.class)) {
                    typeInfo = createBasicTypeInfo(clazz, "返回数据");
                }
            } else if (type instanceof ParameterizedType) {
                // 处理嵌套泛型，如List<SomeClass>、PageDTO<SomeClass>等
                ParameterizedType nestedType = (ParameterizedType) type;
                Type[] nestedArgs = nestedType.getActualTypeArguments();

                // 解析外层泛型类型（如List、PageDTO等）
                Class<?> outerClass = (Class<?>) nestedType.getRawType();
                typeInfo = parseClassFieldsWithInheritance(outerClass);

                // 处理所有嵌套的泛型参数
                for (int i = 0; i < nestedArgs.length; i++) {
                    Map<String, Object> nestedTypeInfo = parseGenericType(nestedArgs[i]);
                    if (!nestedTypeInfo.isEmpty()) {
                        // 通过反射检查外层类的字段，找到对应的泛型字段并替换
                        replaceGenericFieldsInType(typeInfo, outerClass, nestedType, nestedTypeInfo, i);
                    }
                }
            } else if (type instanceof TypeVariable) {
                // 处理类型变量，创建占位符信息
                TypeVariable<?> typeVar = (TypeVariable<?>) type;
                typeInfo.put("typeVariable", typeVar.getName());
                typeInfo.put("description", "泛型类型: " + typeVar.getName());
            }
        } catch (Exception e) {
            log.error("解析泛型类型失败: {}", type, e);
        }

        return typeInfo;
    }

    /**
     * 通过反射检查并替换响应中的泛型字段
     */
    private void replaceGenericFieldsInResponse(Map<String, Object> responseInfo, Class<?> rawType,
                                                ParameterizedType parameterizedType, Map<String, Object> genericFieldInfo, int typeArgIndex) {
        try {
            // 获取泛型参数类型
            Type[] actualTypeArguments = parameterizedType.getActualTypeArguments();
            if (actualTypeArguments.length <= typeArgIndex || genericFieldInfo.isEmpty()) {
                return;
            }

            Type targetType = actualTypeArguments[typeArgIndex];

            // 遍历当前类及父类的所有字段
            Class<?> currentClass = rawType;
            while (currentClass != null && currentClass != Object.class) {
                Field[] fields = currentClass.getDeclaredFields();
                for (Field field : fields) {
                    // 跳过静态字段和serialVersionUID
                    if (java.lang.reflect.Modifier.isStatic(field.getModifiers()) ||
                            "serialVersionUID".equals(field.getName())) {
                        continue;
                    }

                    // 检查字段是否是泛型类型
                    Type fieldGenericType = field.getGenericType();
                    if (fieldGenericType instanceof ParameterizedType) {
                        ParameterizedType fieldParamType = (ParameterizedType) fieldGenericType;
                        Type[] fieldTypeArgs = fieldParamType.getActualTypeArguments();

                        // 检查字段的泛型参数是否与目标类型匹配
                        for (Type fieldTypeArg : fieldTypeArgs) {
                            if (isGenericTypeMatch(fieldTypeArg, targetType)) {
                                String fieldName = field.getName();
                                if (responseInfo.containsKey(fieldName)) {
                                    responseInfo.put(fieldName, genericFieldInfo);
                                    return; // 找到匹配的字段后返回
                                }
                            }
                        }
                    } else if (fieldGenericType instanceof TypeVariable) {
                        // 处理类型变量（如 T、E 等）
                        TypeVariable<?> typeVar = (TypeVariable<?>) fieldGenericType;
                        if (isTypeVariableMatch(typeVar, rawType, targetType, typeArgIndex)) {
                            String fieldName = field.getName();
                            if (responseInfo.containsKey(fieldName)) {
                                responseInfo.put(fieldName, genericFieldInfo);
                                return;
                            }
                        }
                    }
                }
                currentClass = currentClass.getSuperclass();
            }
        } catch (Exception e) {
            log.error("替换泛型字段失败", e);
        }
    }

    /**
     * 检查泛型类型是否匹配
     */
    private boolean isGenericTypeMatch(Type fieldType, Type methodType) {
        if (fieldType instanceof TypeVariable && methodType instanceof TypeVariable) {
            return ((TypeVariable<?>) fieldType).getName().equals(((TypeVariable<?>) methodType).getName());
        }
        if (fieldType instanceof Class && methodType instanceof Class) {
            return fieldType.equals(methodType);
        }
        if (fieldType instanceof TypeVariable || methodType instanceof TypeVariable) {
            return true; // 类型变量可以匹配任何类型
        }
        return fieldType.equals(methodType);
    }

    /**
     * 检查类型变量是否匹配（支持类型参数索引）
     */
    private boolean isTypeVariableMatch(TypeVariable<?> typeVar, Class<?> declaringClass, Type actualType, int typeArgIndex) {
        // 获取类的泛型参数声明
        TypeVariable<?>[] classTypeParams = declaringClass.getTypeParameters();
        for (int i = 0; i < classTypeParams.length; i++) {
            if (classTypeParams[i].getName().equals(typeVar.getName())) {
                // 检查类型变量的位置是否与期望的索引匹配
                return i == typeArgIndex;
            }
        }
        return false;
    }

    /**
     * 通过反射检查并替换类型中的泛型字段（用于嵌套泛型处理）
     */
    private void replaceGenericFieldsInType(Map<String, Object> typeInfo, Class<?> outerClass,
                                            ParameterizedType parameterizedType, Map<String, Object> genericFieldInfo, int typeArgIndex) {
        try {
            // 获取泛型参数类型
            Type[] actualTypeArguments = parameterizedType.getActualTypeArguments();
            if (actualTypeArguments.length <= typeArgIndex || genericFieldInfo.isEmpty()) {
                return;
            }

            Type targetType = actualTypeArguments[typeArgIndex];

            // 遍历当前类及父类的所有字段
            Class<?> currentClass = outerClass;
            while (currentClass != null && currentClass != Object.class) {
                Field[] fields = currentClass.getDeclaredFields();
                for (Field field : fields) {
                    // 跳过静态字段和serialVersionUID
                    if (java.lang.reflect.Modifier.isStatic(field.getModifiers()) ||
                            "serialVersionUID".equals(field.getName())) {
                        continue;
                    }

                    // 检查字段是否是泛型类型
                    Type fieldGenericType = field.getGenericType();
                    if (fieldGenericType instanceof ParameterizedType) {
                        ParameterizedType fieldParamType = (ParameterizedType) fieldGenericType;
                        Type[] fieldTypeArgs = fieldParamType.getActualTypeArguments();

                        // 检查字段的泛型参数是否与目标类型匹配
                        for (Type fieldTypeArg : fieldTypeArgs) {
                            if (isGenericTypeMatch(fieldTypeArg, targetType)) {
                                String fieldName = field.getName();
                                if (typeInfo.containsKey(fieldName)) {
                                    typeInfo.put(fieldName, genericFieldInfo);
                                    return;
                                }
                            }
                        }
                    } else if (fieldGenericType instanceof TypeVariable) {
                        // 处理类型变量
                        TypeVariable<?> typeVar = (TypeVariable<?>) fieldGenericType;
                        if (isTypeVariableMatch(typeVar, outerClass, targetType, typeArgIndex)) {
                            String fieldName = field.getName();
                            if (typeInfo.containsKey(fieldName)) {
                                typeInfo.put(fieldName, genericFieldInfo);
                                return;
                            }
                        }
                    }
                }
                currentClass = currentClass.getSuperclass();
            }
        } catch (Exception e) {
            log.error("替换嵌套泛型字段失败", e);
        }
    }

    /**
     * 获取JSON Schema类型
     */
    private String getJsonSchemaType(Class<?> clazz) {
        // 基本数据类型
        if (clazz == int.class || clazz == Integer.class ||
                clazz == long.class || clazz == Long.class ||
                clazz == short.class || clazz == Short.class ||
                clazz == byte.class || clazz == Byte.class) {
            return "integer";
        }
        if (clazz == double.class || clazz == Double.class ||
                clazz == float.class || clazz == Float.class) {
            return "number";
        }
        if (clazz == boolean.class || clazz == Boolean.class) {
            return "boolean";
        }
        if (clazz == String.class || clazz == char.class || clazz == Character.class) {
            return "string";
        }

        // 枚举类型作为string处理
        if (clazz.isEnum()) {
            return "string";
        }

        // 数组和集合类型
        if (clazz.isArray() ||
                java.util.Collection.class.isAssignableFrom(clazz) ||
                java.util.List.class.isAssignableFrom(clazz) ||
                java.util.Set.class.isAssignableFrom(clazz)) {
            return "array";
        }

        // 其他复杂对象
        return "object";
    }

    /**
     * 在properties中替换泛型字段
     */
    private void replaceGenericFieldsInProperties(Map<String, Object> properties, Class<?> rawType,
                                                  ParameterizedType paramType, Type actualType, int typeArgIndex) {
        try {
            // 遍历当前类及父类的所有字段
            Class<?> currentClass = rawType;
            while (currentClass != null && currentClass != Object.class) {
                Field[] fields = currentClass.getDeclaredFields();
                for (Field field : fields) {
                    // 跳过静态字段和serialVersionUID
                    if (java.lang.reflect.Modifier.isStatic(field.getModifiers()) ||
                            "serialVersionUID".equals(field.getName())) {
                        continue;
                    }

                    String fieldName = field.getName();
                    if (!properties.containsKey(fieldName)) {
                        continue;
                    }

                    // 检查字段是否是泛型类型
                    Type fieldGenericType = field.getGenericType();
                    if (fieldGenericType instanceof TypeVariable) {
                        TypeVariable<?> typeVar = (TypeVariable<?>) fieldGenericType;
                        if (isTypeVariableMatch(typeVar, rawType, actualType, typeArgIndex)) {
                            // 替换泛型字段的schema
                            Map<String, Object> fieldSchema = createTypeSchema(
                                    actualType instanceof Class ? (Class<?>) actualType : Object.class,
                                    actualType
                            );
                            // 保留原有的required和description信息
                            Map<String, Object> originalSchema = (Map<String, Object>) properties.get(fieldName);
                            if (originalSchema.containsKey("required")) {
                                fieldSchema.put("required", originalSchema.get("required"));
                            }
                            if (originalSchema.containsKey("description")) {
                                fieldSchema.put("description", originalSchema.get("description"));
                            }
                            properties.put(fieldName, fieldSchema);
                        }
                    } else if (fieldGenericType instanceof ParameterizedType) {
                        ParameterizedType fieldParamType = (ParameterizedType) fieldGenericType;
                        Type[] fieldTypeArgs = fieldParamType.getActualTypeArguments();

                        // 检查字段的泛型参数是否与目标类型匹配
                        for (Type fieldTypeArg : fieldTypeArgs) {
                            if (isGenericTypeMatch(fieldTypeArg, actualType)) {
                                // 替换泛型字段的schema
                                Map<String, Object> fieldSchema = createTypeSchema(
                                        (Class<?>) fieldParamType.getRawType(),
                                        fieldGenericType
                                );
                                // 保留原有的required和description信息
                                Map<String, Object> originalSchema = (Map<String, Object>) properties.get(fieldName);
                                if (originalSchema.containsKey("required")) {
                                    fieldSchema.put("required", originalSchema.get("required"));
                                }
                                if (originalSchema.containsKey("description")) {
                                    fieldSchema.put("description", originalSchema.get("description"));
                                }
                                properties.put(fieldName, fieldSchema);
                                break;
                            }
                        }
                    }
                }
                currentClass = currentClass.getSuperclass();
            }
        } catch (Exception e) {
            log.error("替换properties中的泛型字段失败", e);
        }
    }

    /**
     * 初始化当前项目的包名前缀（基于主应用类的包）
     */
    private void initProjectPackagePres() {
        try {
            // 查找带有@SpringBootApplication注解的主类（项目入口类）
            Map<String, Object> bootBeans = applicationContext.getBeansWithAnnotation(SpringBootApplication.class);
            if (!bootBeans.isEmpty()) {
                // 取第一个主类（通常一个项目只有一个）
                Class<?> mainClass = bootBeans.values().iterator().next().getClass();
                // 获取主类的包名（即项目根包）
                String mainPackage = mainClass.getPackage().getName();
                projectPackagePres.add(mainPackage);
            } else {
                // 未找到主类时，fallback到原有默认前缀
                log.warn("未找到主应用类，使用默认包名前缀");
            }
        } catch (Exception e) {
            // 异常时fallback到默认前缀
            log.error("获取项目包名前缀失败，使用默认值", e);
        }
    }

    /**
     * 初始化 Swagger 适配器
     */
    private void initSwaggerAdapter() {
        SwaggerVersion version = detectSwaggerVersion();
        log.info("检测到的 Swagger 版本: {}", version);

        switch (version) {
            case SWAGGER2:
                swaggerAdapter = new Swagger2Adapter();
                break;
            case SWAGGER3:
                swaggerAdapter = new Swagger3Adapter();
                break;
            default:
                swaggerAdapter = new NoSwaggerAdapter();
                log.warn("未检测到 Swagger 依赖，将使用默认适配器");
                break;
        }
    }

    /**
     * 检测 Swagger 版本
     */
    private SwaggerVersion detectSwaggerVersion() {
        // 检查配置属性
        String configuredVersion = environment.getProperty("biz.safecenter.swagger.version");
        if (StringUtils.isNotBlank(configuredVersion)) {
            if ("swagger2".equalsIgnoreCase(configuredVersion) || "2".equals(configuredVersion)) {
                return SwaggerVersion.SWAGGER2;
            } else if ("swagger3".equalsIgnoreCase(configuredVersion) || "openapi3".equalsIgnoreCase(configuredVersion) || "3".equals(configuredVersion)) {
                return SwaggerVersion.SWAGGER3;
            }
        }

        // 通过类路径检测
        try {
            // 检测 Swagger3/OpenAPI3
            Class.forName("io.swagger.v3.oas.annotations.Operation");
            return SwaggerVersion.SWAGGER3;
        } catch (ClassNotFoundException e) {
            // 继续检测 Swagger2
        }

        try {
            // 检测 Swagger2
            Class.forName("io.swagger.annotations.ApiOperation");
            return SwaggerVersion.SWAGGER2;
        } catch (ClassNotFoundException e) {
            // 都没有找到
        }

        return SwaggerVersion.NONE;
    }

    /**
     * Swagger 注解适配器接口
     */
    private interface SwaggerAnnotationAdapter {
        String getApiOperationValue(Method method);

        String getApiOperationNotes(Method method);

        String getApiParamValue(Parameter parameter);

        String getApiModelValue(Class<?> clazz);

        String getApiModelDescription(Class<?> clazz);

        String getApiModelPropertyValue(Field field);
    }

    /**
     * Swagger2 适配器实现
     */
    private static class Swagger2Adapter implements SwaggerAnnotationAdapter {
        @Override
        public String getApiOperationValue(Method method) {
            try {
                @SuppressWarnings("unchecked")
                Class<? extends Annotation> apiOperationClass = (Class<? extends Annotation>) Class.forName("io.swagger.annotations.ApiOperation");
                Annotation annotation = method.getAnnotation(apiOperationClass);
                if (annotation != null) {
                    Method valueMethod = apiOperationClass.getMethod("value");
                    return (String) valueMethod.invoke(annotation);
                }
            } catch (Exception e) {
                // 忽略异常
            }
            return "";
        }

        @Override
        public String getApiOperationNotes(Method method) {
            try {
                @SuppressWarnings("unchecked")
                Class<? extends Annotation> apiOperationClass = (Class<? extends Annotation>) Class.forName("io.swagger.annotations.ApiOperation");
                Annotation annotation = method.getAnnotation(apiOperationClass);
                if (annotation != null) {
                    Method notesMethod = apiOperationClass.getMethod("notes");
                    return (String) notesMethod.invoke(annotation);
                }
            } catch (Exception e) {
                // 忽略异常
            }
            return "";
        }

        @Override
        public String getApiParamValue(Parameter parameter) {
            try {
                @SuppressWarnings("unchecked")
                Class<? extends Annotation> apiParamClass = (Class<? extends Annotation>) Class.forName("io.swagger.annotations.ApiParam");
                Annotation annotation = parameter.getAnnotation(apiParamClass);
                if (annotation != null) {
                    Method valueMethod = apiParamClass.getMethod("value");
                    return (String) valueMethod.invoke(annotation);
                }
            } catch (Exception e) {
                // 忽略异常
            }
            return "";
        }

        @Override
        public String getApiModelValue(Class<?> clazz) {
            try {
                @SuppressWarnings("unchecked")
                Class<? extends Annotation> apiModelClass = (Class<? extends Annotation>) Class.forName("io.swagger.annotations.ApiModel");
                Annotation annotation = clazz.getAnnotation(apiModelClass);
                if (annotation != null) {
                    Method valueMethod = apiModelClass.getMethod("value");
                    return (String) valueMethod.invoke(annotation);
                }
            } catch (Exception e) {
                // 忽略异常
            }
            return "";
        }

        @Override
        public String getApiModelDescription(Class<?> clazz) {
            try {
                @SuppressWarnings("unchecked")
                Class<? extends Annotation> apiModelClass = (Class<? extends Annotation>) Class.forName("io.swagger.annotations.ApiModel");
                Annotation annotation = clazz.getAnnotation(apiModelClass);
                if (annotation != null) {
                    Method descriptionMethod = apiModelClass.getMethod("description");
                    return (String) descriptionMethod.invoke(annotation);
                }
            } catch (Exception e) {
                // 忽略异常
            }
            return "";
        }

        @Override
        public String getApiModelPropertyValue(Field field) {
            try {
                @SuppressWarnings("unchecked")
                Class<? extends Annotation> apiModelPropertyClass = (Class<? extends Annotation>) Class.forName("io.swagger.annotations.ApiModelProperty");
                Annotation annotation = field.getAnnotation(apiModelPropertyClass);
                if (annotation != null) {
                    Method valueMethod = apiModelPropertyClass.getMethod("value");
                    return (String) valueMethod.invoke(annotation);
                }
            } catch (Exception e) {
                // 忽略异常
            }
            return "";
        }
    }

    /**
     * Swagger3/OpenAPI3 适配器实现
     */
    private static class Swagger3Adapter implements SwaggerAnnotationAdapter {
        @Override
        public String getApiOperationValue(Method method) {
            try {
                @SuppressWarnings("unchecked")
                Class<? extends Annotation> operationClass = (Class<? extends Annotation>) Class.forName("io.swagger.v3.oas.annotations.Operation");
                Annotation annotation = method.getAnnotation(operationClass);
                if (annotation != null) {
                    Method summaryMethod = operationClass.getMethod("summary");
                    String summary = (String) summaryMethod.invoke(annotation);
                    if (StringUtils.isNotBlank(summary)) {
                        return summary;
                    }
                    // 如果 summary 为空，尝试获取 description
                    Method descriptionMethod = operationClass.getMethod("description");
                    return (String) descriptionMethod.invoke(annotation);
                }
            } catch (Exception e) {
                // 忽略异常
            }
            return "";
        }

        @Override
        public String getApiOperationNotes(Method method) {
            try {
                @SuppressWarnings("unchecked")
                Class<? extends Annotation> operationClass = (Class<? extends Annotation>) Class.forName("io.swagger.v3.oas.annotations.Operation");
                Annotation annotation = method.getAnnotation(operationClass);
                if (annotation != null) {
                    Method descriptionMethod = operationClass.getMethod("description");
                    return (String) descriptionMethod.invoke(annotation);
                }
            } catch (Exception e) {
                // 忽略异常
            }
            return "";
        }

        @Override
        public String getApiParamValue(Parameter parameter) {
            try {
                @SuppressWarnings("unchecked")
                Class<? extends Annotation> parameterClass = (Class<? extends Annotation>) Class.forName("io.swagger.v3.oas.annotations.Parameter");
                Annotation annotation = parameter.getAnnotation(parameterClass);
                if (annotation != null) {
                    Method descriptionMethod = parameterClass.getMethod("description");
                    return (String) descriptionMethod.invoke(annotation);
                }
            } catch (Exception e) {
                // 忽略异常
            }
            return "";
        }

        @Override
        public String getApiModelValue(Class<?> clazz) {
            try {
                @SuppressWarnings("unchecked")
                Class<? extends Annotation> schemaClass = (Class<? extends Annotation>) Class.forName("io.swagger.v3.oas.annotations.media.Schema");
                Annotation annotation = clazz.getAnnotation(schemaClass);
                if (annotation != null) {
                    Method nameMethod = schemaClass.getMethod("name");
                    String name = (String) nameMethod.invoke(annotation);
                    if (StringUtils.isNotBlank(name)) {
                        return name;
                    }
                    // 如果 name 为空，尝试获取 title
                    Method titleMethod = schemaClass.getMethod("title");
                    return (String) titleMethod.invoke(annotation);
                }
            } catch (Exception e) {
                // 忽略异常
            }
            return "";
        }

        @Override
        public String getApiModelDescription(Class<?> clazz) {
            try {
                @SuppressWarnings("unchecked")
                Class<? extends Annotation> schemaClass = (Class<? extends Annotation>) Class.forName("io.swagger.v3.oas.annotations.media.Schema");
                Annotation annotation = clazz.getAnnotation(schemaClass);
                if (annotation != null) {
                    Method descriptionMethod = schemaClass.getMethod("description");
                    return (String) descriptionMethod.invoke(annotation);
                }
            } catch (Exception e) {
                // 忽略异常
            }
            return "";
        }

        @Override
        public String getApiModelPropertyValue(Field field) {
            try {
                @SuppressWarnings("unchecked")
                Class<? extends Annotation> schemaClass = (Class<? extends Annotation>) Class.forName("io.swagger.v3.oas.annotations.media.Schema");
                Annotation annotation = field.getAnnotation(schemaClass);
                if (annotation != null) {
                    Method descriptionMethod = schemaClass.getMethod("description");
                    return (String) descriptionMethod.invoke(annotation);
                }
            } catch (Exception e) {
                // 忽略异常
            }
            return "";
        }
    }

    /**
     * 无 Swagger 适配器实现（默认实现）
     */
    private static class NoSwaggerAdapter implements SwaggerAnnotationAdapter {
        @Override
        public String getApiOperationValue(Method method) {
            return "";
        }

        @Override
        public String getApiOperationNotes(Method method) {
            return "";
        }

        @Override
        public String getApiParamValue(Parameter parameter) {
            return "";
        }

        @Override
        public String getApiModelValue(Class<?> clazz) {
            return "";
        }

        @Override
        public String getApiModelDescription(Class<?> clazz) {
            return "";
        }

        @Override
        public String getApiModelPropertyValue(Field field) {
            return "";
        }
    }
}
