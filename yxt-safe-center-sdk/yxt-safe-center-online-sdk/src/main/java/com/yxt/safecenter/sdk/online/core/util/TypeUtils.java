package com.yxt.safecenter.sdk.online.core.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.lang.reflect.Field;
import java.lang.reflect.Parameter;
import java.util.ArrayList;
import java.util.List;

/**
 * 类型工具类
 * 提供类型检测、转换等通用功能
 */
@Slf4j
public class TypeUtils {

    /**
     * 判断是否为基本类型
     */
    public static boolean isBasicType(Class<?> clazz) {
        return clazz.isPrimitive() ||
                clazz == String.class ||
                clazz == Integer.class || clazz == int.class ||
                clazz == Long.class || clazz == long.class ||
                clazz == Double.class || clazz == double.class ||
                clazz == Float.class || clazz == float.class ||
                clazz == Boolean.class || clazz == boolean.class ||
                clazz == Byte.class || clazz == byte.class ||
                clazz == Short.class || clazz == short.class ||
                clazz == Character.class || clazz == char.class ||
                clazz == Void.class || clazz == void.class ||
                Number.class.isAssignableFrom(clazz);
    }

    /**
     * 获取JSON Schema类型
     */
    public static String getJsonSchemaType(Class<?> clazz) {
        // 基本数据类型
        if (clazz == int.class || clazz == Integer.class ||
                clazz == long.class || clazz == Long.class ||
                clazz == short.class || clazz == Short.class ||
                clazz == byte.class || clazz == Byte.class) {
            return "integer";
        }
        if (clazz == double.class || clazz == Double.class ||
                clazz == float.class || clazz == Float.class) {
            return "number";
        }
        if (clazz == boolean.class || clazz == Boolean.class) {
            return "boolean";
        }
        if (clazz == String.class || clazz == char.class || clazz == Character.class) {
            return "string";
        }

        // 枚举类型作为string处理
        if (clazz.isEnum()) {
            return "string";
        }

        // 数组和集合类型
        if (clazz.isArray() ||
                java.util.Collection.class.isAssignableFrom(clazz) ||
                java.util.List.class.isAssignableFrom(clazz) ||
                java.util.Set.class.isAssignableFrom(clazz)) {
            return "array";
        }

        // 其他复杂对象
        return "object";
    }

    /**
     * 获取简单类型名称
     */
    public static String getSimpleTypeName(Class<?> clazz) {
        if (clazz == int.class || clazz == Integer.class) return "integer";
        if (clazz == long.class || clazz == Long.class) return "long";
        if (clazz == double.class || clazz == Double.class) return "double";
        if (clazz == float.class || clazz == Float.class) return "float";
        if (clazz == boolean.class || clazz == Boolean.class) return "boolean";
        if (clazz == String.class) return "string";
        return clazz.getSimpleName();
    }

    /**
     * 判断参数是否必须
     */
    public static boolean isParameterRequired(Parameter parameter) {
        // 检查@NotNull、@NotBlank等验证注解
        if (parameter.isAnnotationPresent(NotNull.class) ||
                parameter.isAnnotationPresent(NotBlank.class) ||
                parameter.isAnnotationPresent(NotEmpty.class)) {
            return true;
        }

        // 检查Spring注解的required属性
        if (parameter.isAnnotationPresent(RequestParam.class)) {
            return parameter.getAnnotation(RequestParam.class).required();
        }
        if (parameter.isAnnotationPresent(RequestHeader.class)) {
            return parameter.getAnnotation(RequestHeader.class).required();
        }
        if (parameter.isAnnotationPresent(PathVariable.class)) {
            return parameter.getAnnotation(PathVariable.class).required();
        }

        return false;
    }

    /**
     * 判断字段是否必须
     */
    public static boolean isFieldRequired(Field field) {
        return field.isAnnotationPresent(NotNull.class) ||
                field.isAnnotationPresent(NotBlank.class) ||
                field.isAnnotationPresent(NotEmpty.class);
    }

    /**
     * 获取枚举的所有值
     */
    public static List<String> getEnumValues(Class<?> enumClass) {
        List<String> enumValues = new ArrayList<>();
        if (enumClass.isEnum()) {
            try {
                Object[] enumConstants = enumClass.getEnumConstants();
                for (Object enumConstant : enumConstants) {
                    enumValues.add(((Enum<?>) enumConstant).name());
                }
            } catch (Exception e) {
                log.error("获取枚举值失败: {}", enumClass.getName(), e);
            }
        }
        return enumValues;
    }

    /**
     * 获取参数名称
     */
    public static String getParameterName(Parameter parameter, String[] parameterNames, int index) {
        // 先尝试从注解中获取名称
        if (parameter.isAnnotationPresent(RequestHeader.class)) {
            RequestHeader requestHeader = parameter.getAnnotation(RequestHeader.class);
            if (StringUtils.isNotBlank(requestHeader.value())) {
                return requestHeader.value();
            }
            if (StringUtils.isNotBlank(requestHeader.name())) {
                return requestHeader.name();
            }
        } else if (parameter.isAnnotationPresent(PathVariable.class)) {
            PathVariable pathVariable = parameter.getAnnotation(PathVariable.class);
            if (StringUtils.isNotBlank(pathVariable.value())) {
                return pathVariable.value();
            }
            if (StringUtils.isNotBlank(pathVariable.name())) {
                return pathVariable.name();
            }
        } else if (parameter.isAnnotationPresent(RequestParam.class)) {
            RequestParam requestParam = parameter.getAnnotation(RequestParam.class);
            if (StringUtils.isNotBlank(requestParam.value())) {
                return requestParam.value();
            }
            if (StringUtils.isNotBlank(requestParam.name())) {
                return requestParam.name();
            }
        }

        // 从参数名发现器获取
        if (parameterNames != null && index < parameterNames.length) {
            return parameterNames[index];
        }

        // 默认名称
        return "param" + index;
    }
}
