# ControllerScanner Swagger 兼容性使用示例

## 1. 基本配置

### 在 application.yml 中配置 Swagger 版本

```yaml
# 方式1: 明确指定使用 Swagger2
swagger:
  version: swagger2

# 方式2: 明确指定使用 Swagger3
swagger:
  version: swagger3

# 方式3: 使用数字版本号
swagger:
  version: 2  # 或者 3
```

## 2. Swagger2 使用示例

### Controller 示例

```java
@RestController
@RequestMapping("/api/v1/users")
@Api(tags = "用户管理")
public class UserController {
    
    @ApiOperation(value = "获取用户信息", notes = "根据用户ID获取用户详细信息")
    @GetMapping("/{id}")
    public ResponseBase<UserInfo> getUser(
        @ApiParam(value = "用户ID", required = true) @PathVariable Long id) {
        // 实现逻辑
        return ResponseBase.success(new UserInfo());
    }
    
    @ApiOperation(value = "创建用户", notes = "创建新用户")
    @PostMapping
    public ResponseBase<UserInfo> createUser(
        @ApiParam(value = "用户信息") @RequestBody CreateUserRequest request) {
        // 实现逻辑
        return ResponseBase.success(new UserInfo());
    }
}
```

### Model 示例

```java
@ApiModel(value = "用户信息", description = "用户基本信息模型")
public class UserInfo {
    @ApiModelProperty(value = "用户ID")
    private Long id;
    
    @ApiModelProperty(value = "用户名", required = true)
    private String username;
    
    @ApiModelProperty(value = "邮箱")
    private String email;
    
    // getter/setter 省略
}

@ApiModel(value = "创建用户请求", description = "创建用户的请求参数")
public class CreateUserRequest {
    @ApiModelProperty(value = "用户名", required = true)
    private String username;
    
    @ApiModelProperty(value = "邮箱", required = true)
    private String email;
    
    // getter/setter 省略
}
```

## 3. Swagger3 使用示例

### Controller 示例

```java
@RestController
@RequestMapping("/api/v1/users")
@Tag(name = "用户管理")
public class UserController {
    
    @Operation(summary = "获取用户信息", description = "根据用户ID获取用户详细信息")
    @GetMapping("/{id}")
    public ResponseBase<UserInfo> getUser(
        @Parameter(description = "用户ID", required = true) @PathVariable Long id) {
        // 实现逻辑
        return ResponseBase.success(new UserInfo());
    }
    
    @Operation(summary = "创建用户", description = "创建新用户")
    @PostMapping
    public ResponseBase<UserInfo> createUser(
        @Parameter(description = "用户信息") @RequestBody CreateUserRequest request) {
        // 实现逻辑
        return ResponseBase.success(new UserInfo());
    }
}
```

### Model 示例

```java
@Schema(name = "用户信息", description = "用户基本信息模型")
public class UserInfo {
    @Schema(description = "用户ID")
    private Long id;
    
    @Schema(description = "用户名", required = true)
    private String username;
    
    @Schema(description = "邮箱")
    private String email;
    
    // getter/setter 省略
}

@Schema(name = "创建用户请求", description = "创建用户的请求参数")
public class CreateUserRequest {
    @Schema(description = "用户名", required = true)
    private String username;
    
    @Schema(description = "邮箱", required = true)
    private String email;
    
    // getter/setter 省略
}
```

## 4. 混合使用场景

如果项目中同时存在 Swagger2 和 Swagger3 的注解，ControllerScanner 会根据配置的版本来解析对应的注解。

### 配置示例

```yaml
# 在过渡期间，可以明确指定使用哪个版本
swagger:
  version: swagger3  # 优先解析 Swagger3 注解
```

## 5. 自动检测示例

如果不配置 `swagger.version`，系统会自动检测：

1. 首先检测是否存在 Swagger3 依赖
2. 如果没有，检测是否存在 Swagger2 依赖
3. 如果都没有，使用默认适配器（不解析 Swagger 注解）

### 启动日志示例

```
2025-08-01 10:00:00.123 INFO  --- ControllerScanner : 检测到的 Swagger 版本: SWAGGER3
2025-08-01 10:00:00.124 INFO  --- ControllerScanner : -------------applicationName: my-service
```

## 6. 故障排除

### 问题1: 注解没有被解析

检查启动日志中的版本检测信息，确认是否正确检测到了 Swagger 版本。

### 问题2: 版本检测错误

明确配置 `swagger.version` 来指定使用的版本：

```yaml
swagger:
  version: swagger2  # 或 swagger3
```

### 问题3: 依赖冲突

确保项目中只引入需要的 Swagger 版本依赖，或者通过配置明确指定使用的版本。
